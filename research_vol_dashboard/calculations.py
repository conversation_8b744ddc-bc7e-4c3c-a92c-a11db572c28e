import logging
import time

import pandas as pd
import utils_general

from .constants import (
    WING_SPREAD_SIGNAL_THRESHOLD,
)
from .typings import (
    CalculationPipeline,
    FlexTimeseriesChart,
    FlexTimeSeriesTarget,
    WingSpreadPreppedData,
)


def extract_latest_points_for_targets(
    data_df: pd.DataFrame, plot_targets: list[FlexTimeSeriesTarget]
) -> dict[str, float]:
    """
    Given a calculator output DataFrame and the corresponding plot targets,
    extract the latest non-null datapoint for each target and return a mapping
    from a human-readable label to the numeric value.

    The label preference order is: trace_title (if present) else target key.
    """
    latest_points: dict[str, float] = {}
    try:
        for tgt in plot_targets:
            target_key = tgt["target"]
            label = tgt.get("trace_title", target_key) or target_key
            if target_key in data_df.columns:
                series = data_df[target_key].dropna()
                if not series.empty:
                    # Ensure series is ordered by timestamp index before taking last
                    try:
                        series = series.sort_index()
                    except Exception:
                        # If sorting fails (non-sortable index), fall back to original order
                        pass
                    latest_points[str(label)] = float(series.iloc[-1])

    except Exception:
        pass
    return latest_points


def wing_spread_calc(
    prepared_data: WingSpreadPreppedData,
) -> tuple[pd.DataFrame, list[FlexTimeSeriesTarget]]:
    """Wing spread calculation using prepared data.

    Args:
        prepared_data (WingSpreadPreppedData):

    Returns:
        Dict mapping qualified names to a dict with keys:
        - "atm_vol": pd.Series
        - one or more wing target keys (e.g., "10delta", "25delta"), each mapping to a pd.Series
    """
    st = time.time()
    smoothed_df = pd.DataFrame()

    for qn, data_series in prepared_data.items():
        qn_tokens = utils_general.get_qfn_and_version(qn)[1]
        tenor = qn_tokens[4]
        currency = qn_tokens[2]
        atm_vol = data_series["atm_vol"]

        # Process each wing target against ATM
        for wing_or_atm_target, vol_series in data_series.items():
            if wing_or_atm_target == "atm_vol":
                continue

            if tenor not in WING_SPREAD_SIGNAL_THRESHOLD:
                logging.error(
                    f"Tenor {tenor} not found in WING_SPREAD_SIGNAL_THRESHOLD. {WING_SPREAD_SIGNAL_THRESHOLD=}"
                    "Skipping"
                )
                continue

            wing_target = wing_or_atm_target
            target_key = f"{currency}_{wing_target}_{tenor}"
            wing_vol = vol_series

            wing_spread = (
                (wing_vol - atm_vol).rolling(4).mean()
                - (wing_vol - atm_vol).rolling(24 * 15).mean()
            ) - WING_SPREAD_SIGNAL_THRESHOLD[tenor]

            wing_spread_df = pd.DataFrame(wing_spread)
            # e.g v2composite.option.BTC.SVI.7d.{wing_target}.1h.wing_spread
            wing_spread_df["qualified_name"] = ".".join(
                [*qn_tokens[:5], wing_target, qn_tokens[5], "wing_spread"]
            )

            wing_spread_df.columns = pd.Index([target_key, "qualified_name"])
            wing_spread_df = wing_spread_df.dropna()
            smoothed_df = pd.concat([smoothed_df, wing_spread_df], axis=0)

    if smoothed_df.empty:
        logging.warning(
            "Wing spread calc produced empty data. Returning empty result."
        )
        return smoothed_df, []

    smoothed_df.index.name = "timestamp"

    plot_targets = _create_wing_spread_timeseries_targets(smoothed_df)

    logging.info(f"Computed wing spread in {time.time() - st}s")

    return smoothed_df, plot_targets


def _create_wing_spread_timeseries_targets(
    wing_spread_df: pd.DataFrame,
) -> list[FlexTimeSeriesTarget]:

    plot_targets: list[FlexTimeSeriesTarget] = []
    target_keys = [
        col for col in wing_spread_df.columns if col != "qualified_name"
    ]
    for _target_key in target_keys:
        qualified_series = (
            wing_spread_df[["qualified_name", _target_key]]
            .dropna()["qualified_name"]
            .unique()
        )
        if len(qualified_series) == 0:
            continue

        if len(qualified_series) != 1:
            raise ValueError(
                f"Multiple qualified names found for target {_target_key}: {qualified_series}"
            )
        qualified_name_value = qualified_series[0]
        ft_target = FlexTimeSeriesTarget(
            qualified_name=str(qualified_name_value),
            target=_target_key,
            trace_title=_target_key,
        )
        plot_targets.append(ft_target)

    return plot_targets


def _prepare_wing_spread_data(
    data_df: pd.DataFrame, chart_items: FlexTimeseriesChart
) -> WingSpreadPreppedData:
    """
    Prepare data for wing spread calculation using full chart configuration.

    Args:
        data_df: Raw DataFrame containing volatility data
        chart_items: Full chart configuration with targets and settings

    Returns:
        Dict mapping qualified names to a dict with keys:
        - "wing_vol": pd.Series
        - "atm_vol": pd.Series
    """
    prepared_data: WingSpreadPreppedData = {}

    # Group targets by qualified_name to handle multiple targets per QN
    # qualified_name -> targets ([atm, -10delta])
    qn_targets: dict[str, list[str]] = {}
    for chart_target in chart_items["targets"]:
        qn = chart_target["qualified_name"]
        if qn not in qn_targets:
            qn_targets[qn] = []
        qn_targets[qn].append(chart_target["target"])

    for qn, targets in qn_targets.items():
        unique_targets = set(targets)

        # Find ATM and wing targets
        atm_targets = [t for t in unique_targets if str(t).lower() == "atm"]
        wing_targets = [t for t in unique_targets if str(t).lower() != "atm"]

        # Validate exactly one ATM target
        if len(atm_targets) != 1:
            raise ValueError(
                f"Wing spread calculation requires exactly 1 ATM target for {chart_items['chart_title']=}, {qn=}, got {len(atm_targets)=}"
            )

        # Validate at least one wing target
        if len(wing_targets) == 0:
            raise ValueError(
                f"Wing spread calculation requires at least 1 wing target for {chart_items['chart_title']=}, {qn=}, got {len(wing_targets)=}"
            )

        atm_target = atm_targets[0]

        # Filter data for this qualified name
        qn_data = data_df[
            data_df["qualified_name"].str.contains(qn)
        ]  # qn is unversioned
        if qn_data.empty:
            logging.warning(
                f"No data found for qualified name: {qn}, {chart_items['chart_title']}"
            )
            continue

        # Validate required columns exist
        missing_cols = [
            col for col in unique_targets if col not in qn_data.columns
        ]
        if missing_cols:
            raise ValueError(
                f"Missing columns {missing_cols} for {qn} for {chart_items['chart_title']}"
            )

        # Prepare ATM data
        atm_vol = qn_data[atm_target].resample("h").first().ffill()
        if atm_vol.empty:
            logging.warning(f"Insufficient ATM data for {qn} after resampling")
            continue

        # Prepare data structure for this qualified name
        qn_prepared_data = {"atm_vol": atm_vol}

        # Prepare wing data for each wing target
        valid_wing_targets = []
        for wing_target in wing_targets:
            wing_vol = qn_data[wing_target].resample("h").first().ffill()
            if wing_vol.empty:
                logging.warning(
                    f"Insufficient {wing_target} data for {qn} after resampling"
                )
                continue
            qn_prepared_data[wing_target] = wing_vol
            valid_wing_targets.append(wing_target)

        # Only add to prepared_data if we have at least one valid wing target
        if valid_wing_targets:
            prepared_data[qn] = qn_prepared_data
            logging.info(
                f"Prepared wing spread data for {qn}: atm={atm_target}, wings={valid_wing_targets}"
            )

    return prepared_data


CALCULATION_PIPELINES: dict[str, CalculationPipeline[WingSpreadPreppedData]] = {
    "WING_SPREAD_CALC": CalculationPipeline(
        preparator=_prepare_wing_spread_data, calculator=wing_spread_calc
    )
}
