import functools
import time
from collections.abc import Callable
from typing import Any, cast, get_args

import dash_table
import pandas as pd
import utils_general
from dash import dcc, html

from .calculations import (
    CALCULATION_PIPELINES,
    extract_latest_points_for_targets,
)
from .constants import MIN_TENOR_SIGNAL_MAPPING
from .instruments_aggregator import instrument_aggregate_plotter
from .plotting_utils import (
    make_atm_vol_spread,
    make_futures_term_structure_fig,
    make_perpetual_time_series_fig,
    make_smile_constant_tenor_fig,
    make_smile_listed_expiry_fig,
    make_vol_term_structure_fig,
)
from .sandbox_plots_utils import (
    flex_smile_plots,
    make_flex_timeseries_fig,
)
from .typings import (
    DashboardEventArgs,
    GridItems,
    LambdaReturnContainer,
    PlotTypes,
    VolSpace,
)
from .utils import (
    encode_figure,
    ensure_column_exists,
    log_successful_addition_of_chart,
    log_unsuccessful_addition_of_chart,
    match_data_with_event,
)


def _add_figure_to_container(
    container: GridItems | LambdaReturnContainer,
    title: str,
    chart_id: str,
    plot_chart_function: Callable[..., Any],
    chart_type: PlotTypes,
    chart_name: str,
    encode_images: bool = False,
    extract_datapoints_function: Callable[..., Any] | None = None,
) -> None:
    """
    Adds a figure to the container, either as a Dash component or an encoded image.

    Args:
        container (list[html.Div] | LambdaReturnContainer): The container to add the figure to.
        title (str): The title of the chart.
        chart_id (str): The unique identifier for the chart.
        plot_chart_function (Callable[..., go.Figure]): The function that generates the figure.
        chart_type (str): The type/category of the chart.
        chart_name (str): The name of the chart.
        encode_images (bool): Whether to encode the image instead of adding to container.
    """
    # todo: return some dictionary with image name -> fig and divorce image saving from individual plotting functions
    fig = plot_chart_function()  # function returns a Plotly figure

    if encode_images:
        assert isinstance(container, dict)
        encoded_image = encode_figure(fig)
        if chart_type not in container:
            container[chart_type] = {chart_name: {}}
        container[chart_type][chart_name]["image"] = encoded_image

        if extract_datapoints_function is not None:
            container[chart_type][chart_name][
                "data"
            ] = extract_datapoints_function()

    else:
        assert isinstance(container, list)

        container.append(
            html.Div(
                className="grid-item",
                children=[
                    html.H3(
                        title,
                        style={
                            "text-align": "center",
                            "color": "white",
                            "font-size": "20px",
                            "background-color": "#101A2E",
                            "padding": "10px",
                        },
                    ),
                    dcc.Graph(id=chart_id, figure=fig),
                ],
            )
        )


def _add_inversion_monitor_tenor_grid(
    inversion_monitor_tenors: list[int],
    container: GridItems | LambdaReturnContainer,
) -> None:
    # Tenor signal mapping
    table_data = {
        tenor: [
            MIN_TENOR_SIGNAL_MAPPING.get(row_tenor, {}).get(tenor) or "-"
            for row_tenor in inversion_monitor_tenors
        ]
        for tenor in inversion_monitor_tenors
    }

    for col in table_data:
        table_data[col] = [
            f"{round(value * 100, 2)}%" if isinstance(value, float) else value
            for value in table_data[col]
        ]

    tenor_table = pd.DataFrame(table_data, index=inversion_monitor_tenors).T
    data_for_table = tenor_table.reset_index().to_dict("records")
    columns_for_table = [{"name": "Tenor", "id": "index"}] + [
        {"name": str(col), "id": str(col)} for col in tenor_table.columns
    ]

    container.append(
        html.Div(
            className="grid-item",
            children=[
                html.H3(
                    "Tenor Signal Mapping Table",
                    style={
                        "text-align": "center",
                        "color": "white",
                        "font-size": "20px",
                        "background-color": "#101A2E",
                        "padding": "10px",
                    },
                ),
                dash_table.DataTable(
                    data=data_for_table,
                    columns=columns_for_table,
                    style_cell={
                        "textAlign": "center",
                        "backgroundColor": "#101A2E",
                        "color": "white",
                    },
                    style_header={
                        "backgroundColor": "#262b3d",
                        "fontWeight": "bold",
                    },
                ),
            ],
        )
    )
    return


def _add_chart_to_grid_with_logging(
    container: GridItems | LambdaReturnContainer,
    title: str,
    chart_id: str,
    plot_chart_function: Callable[..., Any],
    chart_name: str,
    chart_type: PlotTypes,
    encode_images: bool = False,
    extract_datapoints_function: Callable[..., Any] | None = None,
) -> None:
    st = time.time()
    try:
        _add_figure_to_container(
            container=container,
            title=title,
            chart_id=chart_id,
            plot_chart_function=plot_chart_function,
            extract_datapoints_function=extract_datapoints_function,
            chart_name=chart_name,
            chart_type=chart_type,
            encode_images=encode_images,
        )
        log_successful_addition_of_chart(chart=chart_name, start_time=st)
    except Exception:
        log_unsuccessful_addition_of_chart(chart=chart_name, start_time=st)


def _add_inversion_monitor_chart(
    container: GridItems | LambdaReturnContainer,
    event_args: DashboardEventArgs,
    plotting_data: dict[str, pd.DataFrame],
    encode_images: bool = False,
) -> None:
    plot_objects = event_args["plot_objects"]
    currencies = event_args["currencies"]
    series_to_plot = plot_objects["inversion_monitor"]["series_to_plot"]
    end_time = event_args["end_time"]

    inversion_monitor_tenors = plot_objects["inversion_monitor"]["tenors"]
    chart_id = "Inversion Monitor BTC"
    title = f"SABR {series_to_plot.upper()} Term structure inversion indicator"

    plot_chart_function = functools.partial(
        make_atm_vol_spread,
        data_df=plotting_data["inversion_monitor"],
        model="SABR",
        tenors=inversion_monitor_tenors,
        currency=currencies[0],
        series_to_plot=series_to_plot,
        end_time=end_time,
    )

    _add_chart_to_grid_with_logging(
        container=container,
        encode_images=encode_images,
        title=title,
        chart_id=chart_id,
        plot_chart_function=plot_chart_function,
        chart_name="Inversion monitor",
        chart_type="inversion_monitor",
    )

    _add_inversion_monitor_tenor_grid(inversion_monitor_tenors, container)


def _add_perpetual_charts(
    container: GridItems | LambdaReturnContainer,
    event_args: DashboardEventArgs,
    plotting_data: dict[str, pd.DataFrame],
    encode_images: bool = False,
) -> None:
    currencies = event_args["currencies"]
    end_time = event_args["end_time"]

    for i, currency in enumerate(currencies):
        chart_id = f"chart-perpetual-{i}"
        title = f"{currency} Perpetual Timeseries"
        plot_chart_function = functools.partial(
            make_perpetual_time_series_fig,
            data_df=plotting_data["perpetual"],
            currency=currency,
            series_to_plot="rate",
            end_time=end_time,
        )
        _add_chart_to_grid_with_logging(
            container=container,
            encode_images=encode_images,
            title=title,
            chart_id=chart_id,
            plot_chart_function=plot_chart_function,
            chart_name=f"Perpetual- {currency}",
            chart_type="perpetual",
        )


def _add_volatility_term_structure_charts(
    container: GridItems | LambdaReturnContainer,
    event_args: DashboardEventArgs,
    plotting_data: dict[str, pd.DataFrame],
    encode_images: bool = False,
) -> None:
    currencies = event_args["currencies"]
    plot_objects = event_args["plot_objects"]

    for i, currency in enumerate(currencies):
        chart_id = f"chart-{currency}-Volatility-Term_structure-{i}"
        title = f"{currency} Vol Term Structures"
        plot_chart_function = functools.partial(
            make_vol_term_structure_fig,
            data_df=plotting_data["volatility_term_structure"],
            model=plot_objects["volatility_term_structure"]["model"],
            currency=currency,
        )
        _add_chart_to_grid_with_logging(
            container=container,
            encode_images=encode_images,
            title=title,
            chart_id=chart_id,
            plot_chart_function=plot_chart_function,
            chart_name=f"{currency} volatility_term_structure",
            chart_type="volatility_term_structure",
        )


def _add_futures_term_structure_chart(
    container: GridItems | LambdaReturnContainer,
    event_args: DashboardEventArgs,
    plotting_data: dict[str, pd.DataFrame],
    encode_images: bool = False,
) -> None:
    plot_objects = event_args["plot_objects"]
    chart_id = "chart-SpotYields TERM Structures"
    title = "Spot Yields"
    plot_chart_function = functools.partial(
        make_futures_term_structure_fig,
        data_df=plotting_data["futures_term_structure"],
        currencies=plot_objects["futures_term_structure"]["underlyings"],
    )
    _add_chart_to_grid_with_logging(
        container=container,
        encode_images=encode_images,
        title=title,
        chart_id=chart_id,
        plot_chart_function=plot_chart_function,
        chart_name="futures_term_structure",
        chart_type="futures_term_structure",
    )


def _add_smiles_charts(
    container: GridItems | LambdaReturnContainer,
    event_args: DashboardEventArgs,
    plotting_data: dict[str, pd.DataFrame],
    encode_images: bool = False,
) -> None:
    currencies = event_args["currencies"]
    plot_objects = event_args["plot_objects"]

    # Listed Tenor Smiles
    for i, currency in enumerate(currencies):
        chart_id = f"chart-{currency}-Listed-Tenor-Smiles-{i}"
        title = f"{currency} Listed Tenor Smiles"
        plot_chart_function = functools.partial(
            make_smile_listed_expiry_fig,
            data_df=plotting_data["smiles"],
            currency=currency,
            exchange=plot_objects["smiles"]["listed_expiry_exchange"],
            expiry_to_plot=plot_objects["smiles"]["expiry_to_plot"],
        )
        _add_chart_to_grid_with_logging(
            container=container,
            encode_images=encode_images,
            title=title,
            chart_id=chart_id,
            plot_chart_function=plot_chart_function,
            chart_name=f"{currency} Smiles",
            chart_type="smiles",
        )

    # Constant Maturity Smiles
    for i, currency in enumerate(currencies):
        for j, model in enumerate(plot_objects["smiles"]["models"]):
            chart_id = f"chart-{currency}-Constant-Maturity-Smiles-{i}-{j}"
            title = f"{currency} Constant Maturity Smiles"
            plot_chart_function = functools.partial(
                make_smile_constant_tenor_fig,
                data_df=plotting_data["smiles"],
                model=model,
                currency=currency,
            )
            _add_chart_to_grid_with_logging(
                container=container,
                encode_images=encode_images,
                title=title,
                chart_id=chart_id,
                plot_chart_function=plot_chart_function,
                chart_name=f"{currency} {model} constant maturity smiles",
                chart_type="smiles",
            )


def _add_instruments_aggregator_charts(
    container: GridItems | LambdaReturnContainer,
    event_args: DashboardEventArgs,
    plotting_data: dict[str, pd.DataFrame],
    encode_images: bool = False,
) -> None:
    plot_objects = event_args["plot_objects"]

    for chart_name, chart_items in plot_objects["instruments_aggregator"][
        "charts"
    ].items():
        plot_chart_function = functools.partial(
            instrument_aggregate_plotter,
            data=plotting_data["instruments_aggregator"],
            chart_items=chart_items,
            chart_name=chart_name,
            start=event_args["start_time"],
            end=event_args["end_time"],
        )
        _add_chart_to_grid_with_logging(
            container=container,
            encode_images=encode_images,
            title=chart_name,
            chart_id=chart_name,
            plot_chart_function=plot_chart_function,
            chart_name=f"instruments_aggregator {chart_name}",
            chart_type="instruments_aggregator",
        )


def _add_v2timeseries_charts(
    container: GridItems | LambdaReturnContainer,
    event_args: DashboardEventArgs,
    plotting_data: dict[str, pd.DataFrame],
    encode_images: bool = False,
) -> None:
    plot_objects = event_args["plot_objects"]
    for chart_name, chart_items in plot_objects["v2timeseries"][
        "charts"
    ].items():
        extract_datapoints_function = None

        chart_type = chart_items.get("chart_type", "timeseries")
        if "calculation" in chart_items:
            try:
                chart_calc_name = chart_items["calculation"]

                pipeline = CALCULATION_PIPELINES.get(chart_calc_name)
                if pipeline is None:
                    raise ValueError(
                        f"Unsupported calculation chart type {chart_calc_name}"
                    )

                preparator = pipeline.preparator
                calculator = pipeline.calculator

                prepared_data = preparator(
                    plotting_data["v2timeseries"], chart_items
                )
                data, plot_targets = calculator(prepared_data)

                if not plot_targets:
                    raise ValueError(
                        f"Failed to generate plot targets for {chart_name=}, {chart_calc_name=}, skipping"
                    )

                # Optionally extract latest datapoints for calculation charts
                include_datapoints = bool(
                    chart_items.get("include_latest_datapoints", False)
                )
                if include_datapoints and encode_images:
                    extract_datapoints_function = functools.partial(
                        extract_latest_points_for_targets,
                        data_df=data,
                        plot_targets=plot_targets,
                    )

            except Exception:
                log_unsuccessful_addition_of_chart(
                    chart=chart_name, start_time=time.time()
                )
                continue

            plot_chart_function = functools.partial(
                make_flex_timeseries_fig,
                data_df=data,
                target_to_plot=plot_targets,
                yaxis_title=chart_items["yaxis_title"],
                chart_type=chart_type,
                chart_name=chart_name,
                chart_title=chart_items["chart_title"],
                tickprefix=chart_items.get("tickprefix", None),
                ticksuffix=chart_items.get(
                    "ticksuffix",
                    None,
                ),
            )
        else:
            plot_chart_function = functools.partial(
                make_flex_timeseries_fig,
                data_df=plotting_data["v2timeseries"],
                target_to_plot=chart_items["targets"],
                yaxis_title=chart_items["yaxis_title"],
                chart_type=chart_type,
                chart_name=chart_name,
                chart_title=chart_items["chart_title"],
                tickprefix=chart_items.get("tickprefix", None),
                ticksuffix=chart_items.get(
                    "ticksuffix",
                    None,
                ),
            )

        _add_chart_to_grid_with_logging(
            container=container,
            encode_images=encode_images,
            title=chart_name,
            chart_id=chart_name,
            plot_chart_function=plot_chart_function,
            extract_datapoints_function=extract_datapoints_function,
            chart_name=chart_name,
            chart_type="v2timeseries",
        )


def _add_v2smiles_charts(
    container: GridItems | LambdaReturnContainer,
    event_args: DashboardEventArgs,
    plotting_data: dict[str, pd.DataFrame],
    encode_images: bool = False,
) -> None:
    plot_objects = event_args["plot_objects"]
    v2_smiles_charts = plot_objects["v2smiles"]["charts"]

    for chart_name, chart_items in v2_smiles_charts.items():
        st = time.time()
        try:
            plotting_data[chart_name].reset_index(inplace=True)

            ensure_column_exists(
                plotting_data[chart_name], "target_expiries", lambda: []
            )
            ensure_column_exists(
                plotting_data[chart_name],
                "trace_details",
                utils_general.nested_dict,
            )

            smile_type = chart_name.split(".")[-1]
            if smile_type not in get_args(VolSpace):
                raise NotImplementedError(f"Invalid smile type {smile_type}")
            smile_type = cast(VolSpace, smile_type)

            yaxis_title = chart_items["yaxis_title"]
            chart_title = chart_items["chart_title"]

            for index, row in plotting_data[chart_name].iterrows():
                date = row["date"]
                for target in chart_items["targets"]:
                    snapshot = target["snapshot"]

                    # check for matching data and append aux details to snapshot
                    if pd.to_datetime(snapshot, unit="ns") != date:
                        continue

                    model = target["model"]
                    currency = target["currency"]
                    exchange = target["exchange"]
                    color = target["color"]
                    trace_title = target["trace_title"]
                    tenor = target.get("tenor")
                    listed_expiry = target.get("listed_expiry")
                    tenor_target = f"{tenor}d" if tenor else "listed"

                    is_matching_data = match_data_with_event(
                        data_row=row,
                        currency=currency,
                        model=model,
                        exchange=exchange,
                        target=tenor_target,
                    )

                    if is_matching_data:
                        trace_details = plotting_data[chart_name].at[
                            index, "trace_details"
                        ]
                        trace_details.setdefault(tenor or listed_expiry, {})
                        trace_details[tenor or listed_expiry][
                            "trace_title"
                        ] = trace_title
                        trace_details[tenor or listed_expiry]["color"] = color
                        if tenor_target == "listed":
                            plotting_data[chart_name].at[
                                index, "target_expiries"
                            ].append(listed_expiry)

            plot_chart_function = functools.partial(
                flex_smile_plots,
                data_df=plotting_data[chart_name],
                vol_space=smile_type,
                chart_name=chart_name,
                yaxis_title=yaxis_title,
                chart_title=chart_title,
            )
            _add_chart_to_grid_with_logging(
                container=container,
                encode_images=encode_images,
                title=chart_name,
                chart_id=chart_name,
                plot_chart_function=plot_chart_function,
                chart_name=f"v2smiles {chart_name}",
                chart_type="v2smiles",
            )
        except Exception:
            log_unsuccessful_addition_of_chart(
                chart=f"v2smiles {chart_name}", start_time=st
            )


def get_charts(
    event_args: DashboardEventArgs,
    plotting_data: dict[str, pd.DataFrame],
    encode_images: bool = False,
) -> GridItems | LambdaReturnContainer:
    container: GridItems | LambdaReturnContainer

    if encode_images:
        container = {}
    else:
        container = []

    if (
        "inversion_monitor" in event_args["plot_objects"]
        and not plotting_data["inversion_monitor"].empty
    ):
        _add_inversion_monitor_chart(
            container=container,
            event_args=event_args,
            plotting_data=plotting_data,
            encode_images=encode_images,
        )

    if (
        "perpetual" in event_args["plot_objects"]
        and not plotting_data["perpetual"].empty
    ):
        _add_perpetual_charts(
            container=container,
            event_args=event_args,
            plotting_data=plotting_data,
            encode_images=encode_images,
        )

    if (
        "volatility_term_structure" in event_args["plot_objects"]
        and not plotting_data["volatility_term_structure"].empty
    ):
        _add_volatility_term_structure_charts(
            container=container,
            event_args=event_args,
            plotting_data=plotting_data,
            encode_images=encode_images,
        )

    if (
        "futures_term_structure" in event_args["plot_objects"]
        and not plotting_data["futures_term_structure"].empty
    ):
        _add_futures_term_structure_chart(
            container=container,
            event_args=event_args,
            plotting_data=plotting_data,
            encode_images=encode_images,
        )

    if (
        "smiles" in event_args["plot_objects"]
        and not plotting_data["smiles"].empty
    ):
        _add_smiles_charts(
            container=container,
            event_args=event_args,
            plotting_data=plotting_data,
            encode_images=encode_images,
        )

    if (
        "instruments_aggregator" in event_args["plot_objects"]
        and not plotting_data["instruments_aggregator"].empty
    ):
        _add_instruments_aggregator_charts(
            container=container,
            event_args=event_args,
            plotting_data=plotting_data,
            encode_images=encode_images,
        )

    if (
        "v2timeseries" in event_args["plot_objects"]
        and not plotting_data["v2timeseries"].empty
    ):
        _add_v2timeseries_charts(
            container=container,
            event_args=event_args,
            plotting_data=plotting_data,
            encode_images=encode_images,
        )

    if "v2smiles" in event_args["plot_objects"]:
        _add_v2smiles_charts(
            container=container,
            event_args=event_args,
            plotting_data=plotting_data,
            encode_images=encode_images,
        )

    return container
