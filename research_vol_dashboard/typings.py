from dataclasses import dataclass, field
from datetime import datetime
from typing import (
    Any,
    Generic,
    Literal,
    NotRequired,
    Protocol,
    TypedDict,
    TypeVar,
    Union,
)

import numpy as np
import pandas as pd
from dash.development.base_component import Component
from datagrabber import CatalogAssetType, CatalogItem, GrabParams
from numpy.typing import NDArray
from utils_aws import CalcBase, DateRange
from utils_calc import Model
from utils_time_series import Frequency

NDArrayFloat64 = NDArray[np.float64]

PlotTypes = Literal[
    "perpetual",
    "inversion_monitor",
    "smiles",
    "v2timeseries",
    "futures_term_structure",
    "volatility_term_structure",
    "instruments_aggregator",
    "v2smiles",
]
AggregationLevels = Literal[
    "exchange", "currency", "expiry", "instrument", "strike", "option_type"
]
AggregationFilterKeys = Literal[
    "exchanges", "currencies", "expiries", "instruments", "option_types"
]
ChartType = Literal["bar", "stacked_area", "timeseries"]
XValues = Union["pd.Series[datetime]", list[float], NDArrayFloat64]
YValues = Union[
    "pd.Series[float]", "pd.Series[int]", list[float], NDArrayFloat64
]

ChartName = str
ChartIdObjects = Any

ChartCategory = TypeVar("ChartCategory")

GridItems = list[Component]


class LambdaPayload(TypedDict, total=False):
    image: str  # image base64 image string
    data: Any


# Encoded charts container: chart_category (v2timeseries etc) -> chart_name -> payload (base64 image string or auxiliary metadata)
LambdaReturnContainer = dict[str, dict[str, LambdaPayload]]

VolSpace = Literal["delta", "moneyness"]


class GenericChartItem(TypedDict, Generic[ChartCategory]):
    charts: dict[ChartName, ChartCategory]


class BaseChartDetails(TypedDict):
    yaxis_title: str
    chart_title: str


class SmileFlexEvent(TypedDict):
    tenor: NotRequired[int]
    listed_expiry: NotRequired[str]
    exchange: str
    currency: str
    chart_id: int


# isostamp -> SmileFlexEvent
Smiles = dict[str, SmileFlexEvent]


class InversionMonitor(TypedDict):
    tenors: list[int]
    model: Model
    exchange: str
    series_to_plot: str


class StandardSmile(TypedDict):
    constant_tenor_exchange: str
    models: list[Model]
    currencies: list[str]
    calc_types: list[str]
    tenors: list[int]
    snapshot_timestamps: list[str]
    expiry_to_plot: str
    listed_expiry_exchange: str
    listed_expiry_timestamp: str


class Perpetuals(TypedDict):
    exchange: str


class FlexSmilesTarget(TypedDict):
    tenor: int
    exchange: str
    currency: str
    model: Model
    snapshot: str
    trace_title: str
    color: str


class FlexSmilesChart(BaseChartDetails):
    targets: list[FlexSmilesTarget]


class V2Smiles(TypedDict):
    charts: dict[ChartName, FlexSmilesChart]


class ExchangeData(TypedDict):
    exchange: str
    original_quote_asset: str


class AggregationFilters(TypedDict):
    exchanges: list[ExchangeData]
    expiries: NotRequired[list[str]]
    instruments: NotRequired[list[str]]
    currencies: NotRequired[list[str]]
    option_types: NotRequired[Literal["C", "P"]]


class ChartFormats(TypedDict):
    chart_type: ChartType
    show_full_legend: bool
    invert_aggregation_levels: list[str]
    dollar_denomination: bool


class AggregateInstrumentsChart(BaseChartDetails):
    aggregation_level: AggregationLevels
    sub_aggregation_level: NotRequired[AggregationLevels]
    filters: AggregationFilters
    target_suffix: Literal["volume", "open_interest"]
    asset_type: CatalogAssetType
    currencies: list[str]
    resample_frequency: str
    chart_formats: ChartFormats


class AggregateInstruments(GenericChartItem[AggregateInstrumentsChart]):
    pass


class FlexTimeSeriesTarget(TypedDict):
    qualified_name: str
    target: str
    trace_title: NotRequired[str]
    color: NotRequired[str]
    # specifying this will mutate the qualified_name
    resample_config: NotRequired[Frequency]
    sub_query_interval: NotRequired[int]  # in seconds


class FlexTimeseriesChart(TypedDict):
    yaxis_title: str
    calculation: NotRequired[str]
    # some custom need more historical data due to rolling windows etc
    additional_lookback_days: NotRequired[int]
    chart_title: str
    chart_type: NotRequired[ChartType]
    targets: list[FlexTimeSeriesTarget]
    ticksuffix: NotRequired[str]
    tickprefix: NotRequired[str]
    # When True for calculation charts, include latest datapoints in Lambda output
    include_latest_datapoints: NotRequired[bool]


class FlexTimeSeries(GenericChartItem[FlexTimeseriesChart]):
    pass


class VolatilityTermStructure(TypedDict):
    snapshot_timestamps: list[str]
    exchange: str
    model: str
    tenors: list[int]


class TermStructureBase(TypedDict):
    exchange: str
    snapshot_timestamps: list[str]
    tenors: list[int]
    underlyings: NotRequired[list[str]]
    model: NotRequired[Model]


class PlotObjects(TypedDict, total=False):
    perpetual: Perpetuals
    inversion_monitor: InversionMonitor
    smiles: StandardSmile
    v2smiles: V2Smiles
    v2timeseries: FlexTimeSeries
    futures_term_structure: TermStructureBase
    aggregate_instruments: object  # todo: cleanup
    instruments_aggregator: AggregateInstruments  # todo: cleanup
    volatility_term_structure: TermStructureBase


class Args(TypedDict):
    debug: bool
    exchanges: list[str]
    models: list[Model]
    currencies: list[str]
    frequency: Frequency
    date_range: DateRange
    plot_objects: PlotObjects


class ResearchVolDashboard(CalcBase):
    args: Args


class DashboardEventArgs(TypedDict):
    end_time: str
    exchanges: list[str]
    start_time: str
    periods: int
    models: list[Model]
    currencies: list[str]
    plot_objects: PlotObjects
    output_version: str  # todo: check to see if really needed else remove


class Tick(TypedDict):
    title: str
    tickmode: str
    tickvals: list[int]
    ticktext: list[str]


@dataclass
class InstrumentsGrab:
    grab_params: list[GrabParams]
    instruments: list[CatalogItem] = field(default_factory=list)


@dataclass
class GrabbedResult:
    data: list[dict[str, Any]]
    instruments: list[CatalogItem] = field(default_factory=list)


PreparedOutT = TypeVar("PreparedOutT", covariant=True)
PreparedInT = TypeVar("PreparedInT", contravariant=True)


class Preparator(Protocol[PreparedOutT]):
    """Callable that transforms raw data + chart config into prepared data."""

    def __call__(
        self,
        data_df: pd.DataFrame,
        chart_items: FlexTimeseriesChart,
    ) -> PreparedOutT: ...


class Calculator(Protocol[PreparedInT]):
    """Callable that computes results from prepared data."""

    def __call__(
        self,
        prepared_data: PreparedInT,
    ) -> tuple[pd.DataFrame, list[FlexTimeSeriesTarget]]: ...


PreppedCalcData = TypeVar("PreppedCalcData")


@dataclass
class CalculationPipeline(Generic[PreppedCalcData]):
    """Container for a calculation's preparator and calculator functions."""

    preparator: Preparator[PreppedCalcData]
    calculator: Calculator[PreppedCalcData]


# e.g {"atm_vol": pd.Series, "10delta": pd.Series, "25delta": pd.Series}
WingSeries = dict[Literal["atm_vol"] | str, "pd.Series[Any]"]
# qualified_name -> WingSeries
WingSpreadPreppedData = dict[str, WingSeries]
