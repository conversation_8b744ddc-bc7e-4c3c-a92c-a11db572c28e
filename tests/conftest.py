import json
import pathlib
from datetime import UTC
from typing import Any

import numpy as np
import pandas as pd
import pytest

from research_vol_dashboard.typings import (
    FlexTimeseriesChart,
    FlexTimeSeriesTarget,
    GrabbedResult,
)


def convert_to_json_serializable(obj: Any) -> Any:
    """
    Convert pandas objects and other non-serializable objects to JSON-serializable format.

    Args:
        obj: Object to convert

    Returns:
        JSON-serializable representation of the object
    """

    if isinstance(obj, pd.DataFrame):
        # Convert DataFrame to dict with proper handling of index and timestamps
        result = {
            "index": [convert_to_json_serializable(idx) for idx in obj.index],
            "columns": obj.columns.tolist(),
            "data": [
                [convert_to_json_serializable(val) for val in row]
                for row in obj.values
            ],
            "index_name": obj.index.name,
        }
        return result
    elif isinstance(obj, pd.Series):
        return {
            "index": [convert_to_json_serializable(idx) for idx in obj.index],
            "values": [convert_to_json_serializable(val) for val in obj.values],
            "name": obj.name,
        }
    elif isinstance(obj, pd.Timestamp | pd.DatetimeIndex):
        if hasattr(obj, "isoformat"):
            return obj.isoformat()
        else:
            return str(obj)
    elif isinstance(obj, list):
        return [convert_to_json_serializable(item) for item in obj]
    elif isinstance(obj, dict):
        # Sort dict keys for deterministic ordering, but do not sort lists (ordering matters)
        return {
            key: convert_to_json_serializable(value)
            for key, value in sorted(obj.items(), key=lambda item: item[0])
        }
    elif hasattr(obj, "isoformat"):  # datetime objects
        return obj.isoformat()
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.integer | np.floating):
        return obj.item()
    elif pd.isna(obj):
        return None
    else:
        return obj


def load_fixture(filename: str) -> Any:
    """
    Loads a fixture from the fixtures folder.

    Args:
        filename (str): The name of the fixture file.

    Returns:
        Any: The loaded fixture data.
    """
    fixture_path = (
        pathlib.Path(__file__).parent.resolve() / "input_data" / filename
    )
    with open(fixture_path) as file:
        data = json.load(file)
    return data


@pytest.fixture
def mock_retrieve_data_timeseries() -> dict[str, GrabbedResult]:
    return {
        "v2timeseries": GrabbedResult(
            data=load_fixture("timeseries/timeseries.json"),
            instruments=[],
        )
    }


@pytest.fixture
def mock_event() -> Any:
    """
    Provides a mock event for the lambda_handler.

    """
    return load_fixture("timeseries/event.json")


@pytest.fixture
def mock_wing_spread_event() -> Any:
    """
    Provides a mock event for the lambda_handler.

    """
    return load_fixture("timeseries/wing_spread_event.json")


@pytest.fixture
def mock_context() -> dict[str, Any]:
    return {}


@pytest.fixture
def wing_spread_data_df() -> pd.DataFrame:
    """Load real wing spread data from fixture file."""
    data = load_fixture("timeseries/wing_spread_data.json")
    if not data:
        # Return empty DataFrame if no data in fixture
        return pd.DataFrame()

    df = pd.DataFrame(data)
    if "timestamp" in df.columns:
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        df.set_index("timestamp", inplace=True)
    return df


@pytest.fixture
def mock_wing_spread_data_df() -> pd.DataFrame:
    timestamps = pd.date_range(
        start="2024-01-01 00:00:00",
        end="2024-01-30 23:00:00",
        freq="h",
        tz=UTC,
    )

    data = []
    qualified_names = [
        "v-00004.v2composite.option.BTC.SVI.7d.1h.smile",
        "v-00004.v2composite.option.BTC.SVI.30d.1h.smile",
        "v-00004.v2composite.option.BTC.SVI.60d.1h.smile",
        "v-00004.v2composite.option.BTC.SVI.90d.1h.smile",
        "v-00004.v2composite.option.BTC.SVI.180d.1h.smile",
    ]

    for qn in qualified_names:
        for ts in timestamps:
            atm_vol = 0.7
            delta_10_vol = atm_vol + 0.01

            data.append(
                {
                    "timestamp": ts,
                    "qualified_name": qn,
                    "atm": atm_vol,
                    "10delta": delta_10_vol,
                }
            )

    df = pd.DataFrame(data)
    df.set_index("timestamp", inplace=True)
    return df


@pytest.fixture
def chart_items() -> FlexTimeseriesChart:
    return {
        "yaxis_title": "Butterfly Spread",
        "chart_title": "BTC Call Wing Spread Signal",
        "additional_lookback_days": 15,
        "calculation": "WING_SPREAD_CALC",
        "targets": [
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                trace_title="7d",
                color="#247CFF",
                target="10delta",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.7d.1h.smile",
                target="atm",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.30d.1h.smile",
                trace_title="30d",
                color="#FFCD00",
                target="10delta",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.30d.1h.smile",
                target="atm",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.60d.1h.smile",
                trace_title="60d",
                color="#FF54AF",
                target="10delta",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.60d.1h.smile",
                target="atm",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.90d.1h.smile",
                trace_title="90d",
                color="#E16100",
                target="10delta",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.90d.1h.smile",
                target="atm",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.180d.1h.smile",
                trace_title="180d",
                color="#43FF64",
                target="10delta",
            ),
            FlexTimeSeriesTarget(
                qualified_name="v2composite.option.BTC.SVI.180d.1h.smile",
                target="atm",
            ),
        ],
    }


def set_snapshot_path_to_parent(snapshot: Any) -> Any:
    snapshot.snapshot_dir = pathlib.Path(str(snapshot.snapshot_dir.parent))
    return snapshot
