import base64
import json
from io import By<PERSON><PERSON>
from typing import Any
from unittest.mock import patch

import pytest
from PIL import Image, UnidentifiedImageError

from app import lambda_handler
from research_vol_dashboard.typings import GrabbedResult
from research_vol_dashboard.utils import is_lambda_context

from .conftest import load_fixture


def test_lambda_handler_with_mocked_process_event(
    mock_event: dict[Any, Any],
    mock_context: dict[Any, Any],
    mock_retrieve_data_timeseries: dict[str, GrabbedResult],
) -> None:
    with patch("app.retrieve_data") as mock_retrieve_data:
        mock_retrieve_data.return_value = mock_retrieve_data_timeseries
        response = lambda_handler(mock_event, mock_context)  # type: ignore

        assert is_lambda_context()
        assert response["statusCode"] == 200

        body = json.loads(response["body"])

        assert body["output"]["status"] == 0
        assert body["output"]["msg"] == "Success"

        charts_dict = body["output"]["results"]

        assert isinstance(charts_dict, dict)
        assert charts_dict

        for _chart_type, charts in charts_dict.items():
            assert isinstance(charts, dict)
            for chart_name, payload in charts.items():
                encoded_image = payload["image"]
                # Decode the base64 string
                image_bytes = base64.b64decode(encoded_image)

                # Check that the image bytes start with the PNG signature
                assert image_bytes.startswith(
                    b"\x89PNG\r\n\x1a\n"
                ), f"The image {chart_name} does not start with a valid PNG signature."

                # Try to open the image using PIL to ensure it's a valid PNG
                try:
                    image = Image.open(BytesIO(image_bytes))
                    assert (
                        image.format == "PNG"
                    ), f"The image {chart_name} is not recognized as a PNG format."
                except UnidentifiedImageError:
                    pytest.fail(
                        f"The image {chart_name} could not be identified as a valid image."
                    )


def test_lambda_handler_with_include_latest_datapoints(
    mock_context: dict[Any, Any],
    mock_wing_spread_event: dict[Any, Any],
) -> None:

    with patch("app.retrieve_data") as mock_retrieve_data:
        mock_retrieve_data.return_value = {
            "v2timeseries": GrabbedResult(
                data=load_fixture("timeseries/wing_spread_data.json"),
                instruments=[],
            )
        }

        response = lambda_handler(mock_wing_spread_event, mock_context)  # type: ignore

        assert is_lambda_context()
        assert response["statusCode"] == 200

        body = json.loads(response["body"])

        assert body["output"]["status"] == 0
        assert body["output"]["msg"] == "Success"

        charts_dict = body["output"]["results"]

        assert isinstance(charts_dict, dict)
        assert charts_dict

        for _chart_type, charts in charts_dict.items():
            assert isinstance(charts, dict)
            for chart_name, payload in charts.items():
                encoded_image = payload["image"]

                # Decode the base64 string
                image_bytes = base64.b64decode(encoded_image)

                # Check that the image bytes start with the PNG signature
                assert image_bytes.startswith(
                    b"\x89PNG\r\n\x1a\n"
                ), f"The image {chart_name} does not start with a valid PNG signature."

                # Try to open the image using PIL to ensure it's a valid PNG
                try:
                    image = Image.open(BytesIO(image_bytes))
                    assert (
                        image.format == "PNG"
                    ), f"The image {chart_name} is not recognized as a PNG format."
                except UnidentifiedImageError:
                    pytest.fail(
                        f"The image {chart_name} could not be identified as a valid image."
                    )

                assert payload["data"] == {
                    "BTC_10delta_7d": -0.07124024465094106
                }
