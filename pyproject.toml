[project]
name = "research-vol-dashboard"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aiobotocore>=2.15.2",
    "aws-lambda-typing>=2.20.0",
    "boto3>=1.35.36",
    "dash>=2.18.2",
    "dash-table>=5.0.0",
    "datagrabber~=4.4",
    "debugpy>=1.8.9",
    "matplotlib>=3.9.2",
    "numpy~=1.26",
    "orjson>=3.10.12",
    "pandas~=2.2",
    "plotly>=5.24.1",
    "scikit-learn>=1.5.2",
    "scipy>=1.12.0",
    "seaborn>=0.13.2",
    "types-aiobotocore-dynamodb>=2.15.2",
    "kaleido==0.2.1",
    "utils_calc~=6.1",
    "utils_aws~=3.6",
    "utils_time_series~=3.0",
    "utils_general~=5.0",
]


[tool.pytest.ini_options]
pythonpath = ["."]

[tool.black]
line-length = 80
target-version = ['py311']
include = '\.pyi?$'
exclude = '(venv/*|env/*)'

[tool.ruff]
line-length = 80
extend-exclude = ['venv', 'env']


[tool.ruff.lint]
select = [
    'F', # pyflakes
    'E', # pycodestyle
    'W', # pycodestyle
    'I', # isort
    'UP', # pyupgrade
    'B', # flake8-bugbear
    'C', # flake8-comprehensions
    'DTZ', # flake8-datetimez
    'RUF', # ruff
]

ignore = [
    'E501', # line too long, handled by black
    'C901', # complex structure, not needed
    'E712', # Doesn't work correctly with dataframes
]

[tool.ruff.lint.per-file-ignores]
'__init__.py' = [
    'F401', # unused import
    'E402', # module import not at top of file
]

[tool.mypy]
warn_return_any = true
python_version = '3.11'
warn_unused_configs = true
allow_redefinition = false
disallow_untyped_defs = true
no_implicit_optional = true
check_untyped_defs = false
strict = true
plugins = 'pydantic.mypy'

[tool.pydantic-mypy]
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = true

[tool.uv]
dev-dependencies = [
    "black>=24.10.0",
    "mypy>=1.13.0",
    "pytest>=8.3.3",
    "ruff>=0.7.2",
    "boto3-stubs>=1.35.68",
    "pandas-stubs>=2.2.3.241009",
    "pre-commit==3.5.0",
    "pytest-snapshot>=0.9.0",
]

[tool.uv.sources]
datagrabber = { git = "https://github.com/blockscholes/datagrabber"}
utils-general = { git = "https://github.com/blockscholes/bs-python-utils", subdirectory = "utils_general"}
utils-calc = { git = "https://github.com/blockscholes/bs-python-utils", subdirectory = "utils_calc"}
utils-aws = { git = "https://github.com/blockscholes/bs-python-utils", subdirectory = "utils_aws"}
utils-time-series = { git = "https://github.com/blockscholes/bs-python-utils", subdirectory = "utils_time_series"}

[tool.setuptools.packages.find]
where = "research_vol_dashboard"
